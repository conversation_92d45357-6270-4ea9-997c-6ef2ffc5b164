import dotenv from "dotenv";
dotenv.config();

/**
 * Supabase Configuration
 * Loads configuration from environment variables with fallbacks
 */
export const supabaseConfig = {
  url: process.env.SUPABASE_URL || "https://hrdjfukhzbzksqaupqie.supabase.co",
  serviceRoleKey:
    process.env.SUPABASE_SERVICE_ROLE_KEY ||
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0",
  dbPassword:
    process.env.SUPABASE_DB_PASSWORD ||
    process.env.VITE_DATABASE_PASSWORD ||
    "",
};

// Validation and logging
console.log("✓ Supabase configuration loaded successfully");
console.log("✓ URL:", supabaseConfig.url ? "Configured" : "Missing");
console.log(
  "✓ Service Role Key:",
  supabaseConfig.serviceRoleKey ? "Configured" : "Missing"
);
console.log(
  "✓ Database Password:",
  supabaseConfig.dbPassword
    ? "Optional - using service role key authentication"
    : "Optional - using service role key authentication"
);

// Validation - only check essential config
if (!supabaseConfig.url || !supabaseConfig.serviceRoleKey) {
  console.error("❌ Critical Supabase configuration missing!");
  console.error(
    "Please ensure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set in your .env file"
  );
  console.error("Current config:", {
    url: supabaseConfig.url ? "✓ Set" : "✗ Missing",
    serviceRoleKey: supabaseConfig.serviceRoleKey ? "✓ Set" : "✗ Missing",
  });
  throw new Error("Missing required Supabase configuration");
}

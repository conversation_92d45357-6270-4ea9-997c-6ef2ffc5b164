{"name": "chewyai", "version": "1.0.0", "type": "module", "license": "MIT", "description": "ChewyAI - AI-Powered Study Tool", "scripts": {"dev": "concurrently \"npm run --workspace=frontend dev\" \"npm run --workspace=backend dev\"", "build": "npm run --workspaces --if-present build", "start": "concurrently \"npm run --workspace=frontend start\" \"npm run --workspace=backend start\"", "check": "npm run --workspaces --if-present check", "clean": "rimraf node_modules frontend/node_modules backend/node_modules frontend/dist backend/dist"}, "workspaces": ["frontend", "backend"], "devDependencies": {"concurrently": "^9.1.2", "rimraf": "^5.0.5"}, "dependencies": {"@supabase/supabase-js": "^2.50.0"}}